@tailwind base;
@tailwind components;
@tailwind utilities;

// Import variables
@import 'variables';

// Import partials
@import 'flasher';
@import 'forms';
@import 'space';
@import 'address';
@import 'header';
@import 'calendar';

// Root variables for light mode
:root {
  --bg-color: #{$bg-color-light};
  --text-color: #{$text-color-light};
  --border-color: #{$border-color-light};
}

// Dark mode variables
html.dark {
  --bg-color: #{$bg-color-dark};
  --text-color: #{$text-color-dark};
  --border-color: #{$border-color-dark};
}

// Base styles
body {
  background-color: var(--bg-color);
  color: var(--text-color);
}

.border {
  border-color: var(--border-color);
}

