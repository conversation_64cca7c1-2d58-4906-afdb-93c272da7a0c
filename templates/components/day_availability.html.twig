<div class="mb-6">
    <h3 class="text-lg font-semibold mb-3 text-gray-900 dark:text-white">Disponibilités</h3>
    <div class="flex flex-wrap gap-1 mb-3">
        {% set days = {
            'monday': 'Lun',
            'tuesday': 'Mar',
            'wednesday': 'Mer',
            'thursday': 'Jeu',
            'friday': 'Ven',
            'saturday': 'Sam',
            'sunday': 'Dim'
        } %}
        {% for key, day in days %}
            {% set isAvailable = attribute(availability, 'is' ~ key|capitalize) %}
            <div class="flex flex-col items-center">
                <div class="w-10 h-10 flex items-center justify-center rounded-lg {% if isAvailable %}bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300{% else %}bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500{% endif %}">
                    {{ day|slice(0, 1) }}
                </div>
                <span class="text-xs mt-1 {% if isAvailable %}text-blue-800 dark:text-blue-300{% else %}text-gray-400 dark:text-gray-500{% endif %}">{{ day }}</span>
            </div>
        {% endfor %}
    </div>
</div>