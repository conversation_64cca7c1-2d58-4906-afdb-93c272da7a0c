{% extends 'base.html.twig' %}

{% block title %}Admin - Reservations{% endblock %}

{% block content %}
    <div class="container mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Reservation Management</h1>
            <a href="{{ path('app_admin_dashboard') }}" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-600 transition-colors flex items-center font-medium shadow-md">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Dashboard
            </a>
        </div>

        <div class="bg-white shadow-md rounded-lg overflow-hidden dark:bg-gray-800">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                        <tr>
                            <th scope="col" class="px-6 py-3">ID</th>
                            <th scope="col" class="px-6 py-3">Guest</th>
                            <th scope="col" class="px-6 py-3">Desk</th>
                            <th scope="col" class="px-6 py-3">Space</th>
                            <th scope="col" class="px-6 py-3">Date</th>
                            <th scope="col" class="px-6 py-3">Status</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for reservation in reservations %}
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4">{{ reservation.id }}</td>
                                <td class="px-6 py-4">{{ reservation.guest.firstname }} {{ reservation.guest.lastname }}</td>
                                <td class="px-6 py-4">{{ reservation.desk.name }}</td>
                                <td class="px-6 py-4">{{ reservation.desk.space.name }}</td>
                                <td class="px-6 py-4">{{ reservation.reservationDate|date('Y-m-d H:i') }}</td>
                                <td class="px-6 py-4">
                                    {% if reservation.status == 0 %}
                                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300">Pending</span>
                                    {% elseif reservation.status == 1 %}
                                        <span class="bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">Confirmed</span>
                                    {% elseif reservation.status == 2 %}
                                        <span class="bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300">Cancelled</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ path('app_reservation_edit', {'id': reservation.id}) }}" class="font-medium text-green-600 dark:text-green-500 hover:underline" title="Edit Reservation">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                            </svg>
                                        </a>
                                        <button data-modal-target="deleteReservationModal-{{ reservation.id }}" data-modal-toggle="deleteReservationModal-{{ reservation.id }}" class="font-medium text-red-600 dark:text-red-500 hover:underline" title="Delete Reservation">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modals (Flowbite) -->
    {% for reservation in reservations %}
        <!-- Modal for reservation {{ reservation.id }} -->
        <div id="deleteReservationModal-{{ reservation.id }}" tabindex="-1" aria-hidden="true" class="hidden modal-container">
            <div class="modal-content modal-lg">
                <!-- Modal content -->
                <!-- Modal header -->
                <div class="modal-header">
                    <h3 class="modal-title">Delete Reservation</h3>
                    <button type="button" class="modal-close" data-modal-hide="deleteReservationModal-{{ reservation.id }}">
                        <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                        </svg>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <!-- Modal body -->
                <div class="modal-body modal-body-center">
                    <svg class="modal-icon modal-icon-danger" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 11V6m0 8h.01M19 10a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                    </svg>
                    <h3 class="modal-text modal-text-title">
                        Are you sure you want to delete this reservation?
                    </h3>
                    <div class="modal-text modal-text-description">
                        <p class="mb-3">
                            <strong>Desk:</strong> <span class="modal-text-highlight">{{ reservation.desk.name }}</span><br>
                            <strong>Date:</strong> <span class="modal-text-highlight">{{ reservation.reservationDate|date('Y-m-d') }}</span><br>
                            <strong>Guest:</strong> <span class="modal-text-highlight">{{ reservation.guest.firstname }} {{ reservation.guest.lastname }}</span>
                        </p>
                        <p>This action cannot be undone.</p>
                    </div>
                </div>
                <!-- Modal footer -->
                <div class="modal-footer">
                    <button data-modal-hide="deleteReservationModal-{{ reservation.id }}" type="button" class="modal-btn modal-btn-secondary">
                        No, cancel
                    </button>
                    <form method="POST" action="{{ path('app_reservation_delete', {'id': reservation.id}) }}" class="inline">
                        <input type="hidden" name="_token" value="{{ csrf_token('delete_reservation') }}">
                        <input type="hidden" name="_method" value="DELETE">
                        <button type="submit" class="modal-btn modal-btn-danger">
                            Yes, I'm sure
                        </button>
                    </form>
                </div>
            </div>
        </div>
    {% endfor %}
{% endblock %}
