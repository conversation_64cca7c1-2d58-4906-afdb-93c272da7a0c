{% extends 'base.html.twig' %}

{% block title %}My Reservations{% endblock %}

{% block content %}
            <div class="container mx-auto">
                <h1 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">My Reservations</h1>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                                <tr>
                                    <th scope="col" class="px-6 py-3">Space</th>
                                    <th scope="col" class="px-6 py-3">Desk</th>
                                    <th scope="col" class="px-6 py-3">Date</th>
                                    <th scope="col" class="px-6 py-3">Status</th>
                                    <th scope="col" class="px-6 py-3">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for reservation in reservations %}
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">
                                            {{ reservation.desk.space.name }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ reservation.desk.name }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ reservation.reservationDate ? reservation.reservationDate|date('Y-m-d') : 'N/A' }}
                                        </td>
                                        <td class="px-6 py-4">
                                            {{ constant('App\\Entity\\Reservation::RESERVATION_STATUSES')[reservation.status] }}
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ path('app_reservation_show', {'id': reservation.id}) }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline" title="View Reservation">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                </a>
                                                {% if reservation.status != 2 %}
                                                    <a href="{{ path('app_reservation_edit', {'id': reservation.id}) }}" class="font-medium text-green-600 dark:text-green-500 hover:underline" title="Edit Reservation">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                        </svg>
                                                    </a>
                                                    <button data-modal-target="deleteReservationModal-{{ reservation.id }}" data-modal-toggle="deleteReservationModal-{{ reservation.id }}" class="font-medium text-red-600 dark:text-red-500 hover:underline" title="Delete Reservation">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                {% else %}
                                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                                        <td colspan="5" class="px-6 py-4 text-center">
                                            No reservations found. <a href="{{ path('app_space_index') }}" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">Browse spaces</a> to make a reservation.
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
{% endblock %}
