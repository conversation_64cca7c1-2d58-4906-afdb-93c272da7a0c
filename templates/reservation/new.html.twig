{% extends 'base.html.twig' %}

{% block title %}New Reservation{% endblock %}
{% block content %}
            <div class="container mx-auto max-w-2xl">
                <div class="mb-6">
                    <a href="{{ path('app_space_show', {'id': desk.space.id}) }}" class="text-blue-600 hover:underline flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                        Back to Space
                    </a>
                </div>
                {{ form_start(reservation_form, {'attr': {'class': 'space-y-4'}}) }}
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="p-6">
                        <h1 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Reserve a Desk</h1>
                        <div class="mb-6">
                            <div style="display: none !important; visibility: hidden !important; height: 0 !important; width: 0 !important; position: absolute !important; left: -9999px !important;">
                                {{ form_widget(reservation_form.reservationDate, {'attr': {
                                    'data-monday': desk.space.availability.isMonday ? '1' : '0',
                                    'data-tuesday': desk.space.availability.isTuesday ? '1' : '0',
                                    'data-wednesday': desk.space.availability.isWednesday ? '1' : '0',
                                    'data-thursday': desk.space.availability.isThursday ? '1' : '0',
                                    'data-friday': desk.space.availability.isFriday ? '1' : '0',
                                    'data-saturday': desk.space.availability.isSaturday ? '1' : '0',
                                    'data-sunday': desk.space.availability.isSunday ? '1' : '0'
                                }}) }}
                            </div>
                            <div class="flex flex-col md:flex-row md:space-x-4 mt-4">
                                <div class="md:w-1/2 mb-4 md:mb-0">
                                    <div class="p-3 bg-blue-50 text-blue-800 rounded-lg dark:bg-blue-900 dark:text-blue-300">
                                        <div class="flex items-center mb-1">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="font-medium">Available days:</span>
                                        </div>
                                        <ul class="ml-7 list-disc text-sm">
                                            {% set deskAvailability = desk.space.availability %}
                                            {% set days = {
                                                'monday': 'Monday',
                                                'tuesday': 'Tuesday',
                                                'wednesday': 'Wednesday',
                                                'thursday': 'Thursday',
                                                'friday': 'Friday',
                                                'saturday': 'Saturday',
                                                'sunday': 'Sunday'
                                            } %}
                                            {% for key, day in days %}
                                                {% set isAvailable = attribute(deskAvailability, 'is' ~ key|capitalize) %}
                                                {% if isAvailable %}
                                                    <li>{{ day }}</li>
                                                {% endif %}
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>

                                <div class="md:w-1/2">
                                    <div class="p-3 bg-gray-50 text-gray-800 rounded-lg dark:bg-gray-700 dark:text-gray-300">
                                        <div class="flex items-center mb-1">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="font-medium">Hours:</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-4 text-sm text-gray-600 dark:text-gray-400">
                                Available days are highlighted in blue on the calendar.
                            </p>
                        </div>
                        <div class="flex justify-end">
                            {% include 'elements/form/submit.html.twig' with {text: 'Confirm Reservation'} %}
                        </div>
                    </div>
                </div>
                {{ form_end(reservation_form) }}
            </div>
{% endblock %}
