#!/bin/bash

echo "=== FlexOffice - Vérification des Fixtures ==="
echo ""

echo "📊 Statistiques générales :"
symfony console doctrine:query:sql "SELECT 'Utilisateurs' as type, COUNT(*) as total FROM user UNION SELECT 'Espaces', COUNT(*) FROM space UNION SELECT 'Bureaux', COUNT(*) FROM desk UNION SELECT 'Adresses', COUNT(*) FROM address UNION SELECT 'Équipements', COUNT(*) FROM equipment UNION SELECT 'Réservations', COUNT(*) FROM reservation UNION SELECT 'Disponibilités', COUNT(*) FROM availability"

echo ""
echo "👥 Comptes utilisateurs créés :"
symfony console doctrine:query:sql "SELECT email, firstname, lastname, roles FROM user ORDER BY id"

echo ""
echo "🏢 Espaces créés :"
symfony console doctrine:query:sql "SELECT s.name, a.city FROM space s LEFT JOIN address a ON s.address_id = a.id ORDER BY s.id"

echo ""
echo "✅ Fixtures chargées avec succès !"
echo "🔑 Mot de passe pour tous les comptes : 12345678"
echo ""
echo "🌐 Comptes de test :"
echo "   - Admin : <EMAIL>"
echo "   - Host 1 : <EMAIL>"
echo "   - Host 2 : <EMAIL>"
echo "   - Guest 1 : <EMAIL>"
echo "   - Guest 2 : <EMAIL>"
echo "   - Guest 3 : <EMAIL>"
